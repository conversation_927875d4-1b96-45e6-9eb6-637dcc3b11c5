{
  // Editor settings
  "editor.formatOnSave": true,
  "editor.formatOnPaste": true,
  "editor.insertSpaces": true,
  "editor.detectIndentation": false,
  "editor.rulers": [80, 120],
  "editor.wordWrap": "wordWrapColumn",
  "editor.wordWrapColumn": 120,
  "editor.trimAutoWhitespace": true,
  "files.trimTrailingWhitespace": true,
  "files.insertFinalNewline": true,
  "files.trimFinalNewlines": true,

  // C++ specific settings
  "C_Cpp.default.cppStandard": "c++20",
  "C_Cpp.default.intelliSenseMode": "gcc-x64",
  "C_Cpp.default.compilerPath": "",
  "C_Cpp.default.includePath": [
    "${workspaceFolder}/include",
    "${workspaceFolder}/src"
  ],
  "C_Cpp.default.defines": [
    "QT_CORE_LIB",
    "QT_GUI_LIB",
    "QT_WIDGETS_LIB"
  ],
  "C_Cpp.clang_format_style": "file",
  "C_Cpp.clang_format_fallbackStyle": "LLVM",

  // CMake settings
  "cmake.configureOnOpen": false,
  "cmake.buildDirectory": "${workspaceFolder}/build",
  "cmake.generator": "Ninja",
  "cmake.preferredGenerators": ["Ninja", "Unix Makefiles"],

  // File associations
  "files.associations": {
    "*.h": "cpp",
    "*.hpp": "cpp",
    "*.cpp": "cpp",
    "*.cxx": "cpp",
    "*.cc": "cpp",
    "*.c": "c",
    "*.qrc": "xml",
    "*.ui": "xml",
    "*.pro": "makefile",
    "*.pri": "makefile",
    "CMakeLists.txt": "cmake",
    "*.cmake": "cmake"
  },

  // Language specific formatting
  "[cpp]": {
    "editor.defaultFormatter": "ms-vscode.cpptools",
    "editor.tabSize": 4,
    "editor.insertSpaces": true
  },
  "[c]": {
    "editor.defaultFormatter": "ms-vscode.cpptools",
    "editor.tabSize": 4,
    "editor.insertSpaces": true
  },
  "[cmake]": {
    "editor.tabSize": 4,
    "editor.insertSpaces": true
  },
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.tabSize": 2
  },
  "[jsonc]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.tabSize": 2
  },
  "[markdown]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.tabSize": 2,
    "editor.wordWrap": "wordWrapColumn",
    "editor.wordWrapColumn": 100
  },
  "[yaml]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.tabSize": 2
  },

  // Search settings
  "search.exclude": {
    "**/build": true,
    "**/build-*": true,
    "**/node_modules": true,
    "**/.git": true,
    "**/.svn": true,
    "**/.hg": true,
    "**/CVS": true,
    "**/.DS_Store": true,
    "**/Thumbs.db": true,
    "**/*.o": true,
    "**/*.obj": true,
    "**/*.exe": true,
    "**/*.dll": true,
    "**/*.so": true,
    "**/*.dylib": true,
    "**/*.a": true,
    "**/*.lib": true
  },

  // File watcher settings
  "files.watcherExclude": {
    "**/build/**": true,
    "**/build-*/**": true,
    "**/node_modules/**": true,
    "**/.git/objects/**": true,
    "**/.git/subtree-cache/**": true
  },

  // Extension settings
  "clangd.arguments": [
    "--header-insertion=iwyu",
    "--completion-style=detailed",
    "--function-arg-placeholders",
    "--fallback-style=file"
  ],

  // Qt specific
  "qt.searchPaths": [],
  "qt.qmakePath": "",

  // Markdown settings
  "markdownlint.config": {
    "extends": ".markdownlint.json"
  },

  // Git settings
  "git.ignoreLimitWarning": true,

  // Terminal settings
  "terminal.integrated.cwd": "${workspaceFolder}",

  // Exclude patterns for file explorer
  "files.exclude": {
    "**/build": true,
    "**/build-*": true,
    "**/.git": true,
    "**/.svn": true,
    "**/.hg": true,
    "**/CVS": true,
    "**/.DS_Store": true,
    "**/Thumbs.db": true,
    "**/node_modules": true
  }
}

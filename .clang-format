# Configuration for clang-format
# Based on Qt coding style with modern C++ adaptations

BasedOnStyle: LLVM

# Basic formatting
IndentWidth: 4
TabWidth: 4
UseTab: Never
ColumnLimit: 120

# Language specific
Language: Cpp
Standard: c++20

# Indentation
AccessModifierOffset: -4
ConstructorInitializerIndentWidth: 4
ContinuationIndentWidth: 4
IndentCaseLabels: true
IndentGotoLabels: true
IndentPPDirectives: None
IndentWrappedFunctionNames: false

# Alignment
AlignAfterOpenBracket: Align
AlignConsecutiveAssignments: false
AlignConsecutiveDeclarations: false
AlignConsecutiveMacros: false
AlignEscapedNewlines: Right
AlignOperands: true
AlignTrailingComments: true

# Allow
AllowAllArgumentsOnNextLine: true
AllowAllConstructorInitializersOnNextLine: true
AllowAllParametersOfDeclarationOnNextLine: true
AllowShortBlocksOnASingleLine: Never
AllowShortCaseLabelsOnASingleLine: false
AllowShortFunctionsOnASingleLine: All
AllowShortIfStatementsOnASingleLine: Never
AllowShortLambdasOnASingleLine: All
AllowShortLoopsOnASingleLine: false

# Breaking
AlwaysBreakAfterDefinitionReturnType: None
AlwaysBreakAfterReturnType: None
AlwaysBreakBeforeMultilineStrings: false
AlwaysBreakTemplateDeclarations: MultiLine

# Binary operators
BreakBeforeBinaryOperators: None
BreakBeforeTernaryOperators: true

# Braces
BreakBeforeBraces: Custom
BraceWrapping:
  AfterCaseLabel: false
  AfterClass: false
  AfterControlStatement: Never
  AfterEnum: false
  AfterFunction: false
  AfterNamespace: false
  AfterObjCDeclaration: false
  AfterStruct: false
  AfterUnion: false
  AfterExternBlock: false
  BeforeCatch: false
  BeforeElse: false
  BeforeLambdaBody: false
  BeforeWhile: false
  IndentBraces: false
  SplitEmptyFunction: true
  SplitEmptyRecord: true
  SplitEmptyNamespace: true

# Constructor initializers
BreakConstructorInitializers: BeforeColon
BreakInheritanceList: BeforeColon

# String literals
BreakStringLiterals: true

# Comments
CommentPragmas: '^ IWYU pragma:'
FixNamespaceComments: true

# Compact namespaces
CompactNamespaces: false

# Constructor initializer
ConstructorInitializerAllOnOneLineOrOnePerLine: false

# Derive pointer alignment
DerivePointerAlignment: false

# Disable formatting
DisableFormat: false

# Experimental features
ExperimentalAutoDetectBinPacking: false

# Include sorting
IncludeBlocks: Preserve
SortIncludes: true
SortUsingDeclarations: true

# Indentation
IndentCaseBlocks: false
IndentExternBlock: AfterExternBlock

# Keep empty lines
KeepEmptyLinesAtTheStartOfBlocks: true
MaxEmptyLinesToKeep: 1

# Namespace indentation
NamespaceIndentation: None

# Penalties (for line breaking decisions)
PenaltyBreakAssignment: 2
PenaltyBreakBeforeFirstCallParameter: 19
PenaltyBreakComment: 300
PenaltyBreakFirstLessLess: 120
PenaltyBreakString: 1000
PenaltyBreakTemplateDeclaration: 10
PenaltyExcessCharacter: 1000000
PenaltyReturnTypeOnItsOwnLine: 200

# Pointer and reference alignment
PointerAlignment: Left

# Spaces
SpaceAfterCStyleCast: false
SpaceAfterLogicalNot: false
SpaceAfterTemplateKeyword: true
SpaceBeforeAssignmentOperators: true
SpaceBeforeCpp11BracedList: false
SpaceBeforeCtorInitializerColon: true
SpaceBeforeInheritanceColon: true
SpaceBeforeParens: ControlStatements
SpaceBeforeRangeBasedForLoopColon: true
SpaceBeforeSquareBrackets: false
SpaceInEmptyBlock: false
SpaceInEmptyParentheses: false
SpacesBeforeTrailingComments: 1
SpacesInAngles: false
SpacesInCStyleCastParentheses: false
SpacesInConditionalStatement: false
SpacesInContainerLiterals: true
SpacesInParentheses: false
SpacesInSquareBrackets: false

# Raw strings
RawStringFormats:
  - Language: Cpp
    Delimiters:
      - cc
      - CC
      - cpp
      - Cpp
      - CPP
      - 'c++'
      - 'C++'
    CanonicalDelimiter: ''
    BasedOnStyle: google

# Qt specific
QtStyleKeywords: ['slots', 'signals']

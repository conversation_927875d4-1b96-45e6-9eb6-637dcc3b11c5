{"version": "0.2.0", "configurations": [{"name": "Debug FluentQt Demo", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/build/FluentQtDemo", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "Set Disassembly Flavor to Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}], "preLaunchTask": "CMake: Build", "miDebuggerPath": "/usr/bin/gdb", "linux": {"MIMode": "gdb", "miDebuggerPath": "/usr/bin/gdb"}, "osx": {"MIMode": "lldb"}, "windows": {"MIMode": "gdb", "miDebuggerPath": "gdb.exe", "program": "${workspaceFolder}/build/FluentQtDemo.exe"}}, {"name": "Debug Carousel Showcase", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/build/CarouselShowcase", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}], "preLaunchTask": "CMake: Build", "miDebuggerPath": "/usr/bin/gdb", "linux": {"MIMode": "gdb", "miDebuggerPath": "/usr/bin/gdb"}, "osx": {"MIMode": "lldb"}, "windows": {"MIMode": "gdb", "miDebuggerPath": "gdb.exe", "program": "${workspaceFolder}/build/CarouselShowcase.exe"}}, {"name": "Debug Timeline Showcase", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/build/TimelineShowcase", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}], "preLaunchTask": "CMake: Build", "miDebuggerPath": "/usr/bin/gdb", "linux": {"MIMode": "gdb", "miDebuggerPath": "/usr/bin/gdb"}, "osx": {"MIMode": "lldb"}, "windows": {"MIMode": "gdb", "miDebuggerPath": "gdb.exe", "program": "${workspaceFolder}/build/TimelineShowcase.exe"}}, {"name": "Debug Tests", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/build/tests/FluentComponentsTest", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}], "preLaunchTask": "CMake: Build", "miDebuggerPath": "/usr/bin/gdb", "linux": {"MIMode": "gdb", "miDebuggerPath": "/usr/bin/gdb"}, "osx": {"MIMode": "lldb"}, "windows": {"MIMode": "gdb", "miDebuggerPath": "gdb.exe", "program": "${workspaceFolder}/build/tests/FluentComponentsTest.exe"}}]}
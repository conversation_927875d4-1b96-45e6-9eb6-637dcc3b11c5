# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# All files
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true

# C++ files
[*.{cpp,cxx,cc,c,hpp,hxx,hh,h}]
indent_style = space
indent_size = 4
max_line_length = 120

# CMake files
[{CMakeLists.txt,*.cmake}]
indent_style = space
indent_size = 4

# JSON files
[*.json]
indent_style = space
indent_size = 2

# YAML files
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# Markdown files
[*.md]
indent_style = space
indent_size = 2
trim_trailing_whitespace = false

# JavaScript/TypeScript files
[*.{js,ts,jsx,tsx}]
indent_style = space
indent_size = 2

# Python files
[*.py]
indent_style = space
indent_size = 4
max_line_length = 88

# Shell scripts
[*.{sh,bash}]
indent_style = space
indent_size = 2

# Batch files
[*.{bat,cmd}]
end_of_line = crlf
indent_style = space
indent_size = 2

# XML files
[*.xml]
indent_style = space
indent_size = 2

# Qt files
[*.{ui,qrc,pro,pri}]
indent_style = space
indent_size = 4

# Configuration files
[*.{ini,cfg,conf}]
indent_style = space
indent_size = 4

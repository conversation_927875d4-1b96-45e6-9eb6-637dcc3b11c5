{"name": "fluentqt", "version": "1.0.0", "description": "A modern, comprehensive C++ Qt6 library implementing Microsoft's Fluent Design System", "homepage": "https://github.com/ElementAstro/element-fluent-ui", "documentation": "https://elementastro.github.io/element-fluent-ui/", "license": "MIT", "supports": "!uwp", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, {"name": "qtbase", "default-features": false, "features": ["gui", "widgets", "printsupport"]}], "default-features": ["charts"], "features": {"charts": {"description": "Enable chart components using Qt6Charts", "dependencies": [{"name": "qtcharts"}]}, "multimedia": {"description": "Enable multimedia components using Qt6Multimedia", "dependencies": [{"name": "qtmultimedia"}]}, "network": {"description": "Enable network components using Qt6Network", "dependencies": [{"name": "qtbase", "default-features": false, "features": ["network"]}]}}}
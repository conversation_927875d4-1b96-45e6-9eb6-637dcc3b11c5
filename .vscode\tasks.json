{"version": "2.0.0", "tasks": [{"label": "CMake: Configure", "type": "shell", "command": "cmake", "args": ["-B", "build", "-S", ".", "-DCMAKE_BUILD_TYPE=Debug", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "Configure CMake project"}, {"label": "CMake: Build", "type": "shell", "command": "cmake", "args": ["--build", "build", "--config", "Debug", "--parallel"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$gcc"], "detail": "Build the project using CMake", "dependsOn": "CMake: Configure"}, {"label": "CMake: Build Release", "type": "shell", "command": "cmake", "args": ["-B", "build-release", "-S", ".", "-DCMAKE_BUILD_TYPE=Release", "&&", "cmake", "--build", "build-release", "--config", "Release", "--parallel"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$gcc"], "detail": "Build release version of the project"}, {"label": "CMake: Clean", "type": "shell", "command": "cmake", "args": ["--build", "build", "--target", "clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "Clean build artifacts"}, {"label": "Run Tests", "type": "shell", "command": "ctest", "args": ["--test-dir", "build", "--output-on-failure", "--parallel"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "Run all tests using CTest", "dependsOn": "CMake: Build"}, {"label": "Format Code", "type": "shell", "command": "find", "args": ["src", "include", "examples", "tests", "-name", "*.cpp", "-o", "-name", "*.h", "-o", "-name", "*.hpp", "|", "xargs", "clang-format", "-i"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "Format all C++ source files using clang-format"}, {"label": "Generate Documentation", "type": "shell", "command": "doxygen", "args": ["Doxyfile"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "Generate API documentation using Doxygen"}, {"label": "Install Dependencies", "type": "shell", "command": "npm", "args": ["install"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "Install Node.js dependencies for documentation tooling"}, {"label": "Build Documentation", "type": "shell", "command": "npm", "args": ["run", "docs:build"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "Build documentation website", "dependsOn": "Install Dependencies"}, {"label": "Serve Documentation", "type": "shell", "command": "npm", "args": ["run", "docs:dev"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "Start documentation development server", "dependsOn": "Install Dependencies"}]}
# Build directories
build/
build-*/
out/
dist/
bin/
lib/

# CMake generated files
CMakeCache.txt
CMakeFiles/
cmake_install.cmake
Makefile
*.cmake
!CMakeLists.txt
compile_commands.json

# Qt generated files
*_autogen/
*.moc
*.qrc.cpp
ui_*.h
qrc_*.cpp
moc_*.cpp

# Prerequisites
*.d

# Compiled Object files
*.slo
*.lo
*.o
*.obj

# Precompiled Headers
*.gch
*.pch

# Linker files
*.ilk

# Debugger Files
*.pdb

# Compiled Dynamic libraries
*.so
*.dylib
*.dll

# Fortran module files
*.mod
*.smod

# Compiled Static libraries
*.lai
*.la
*.a
*.lib

# Executables
*.exe
*.out
*.app

# debug information files
*.dwo

# Visual Studio
.vs/
*.vcxproj
*.vcxproj.filters
*.vcxproj.user
*.sln
*.suo
*.user
*.aps
*.pch
*.vspscc
*_i.c
*_p.c
*.ncb
*.tlb
*.tlh
*.bak
*.cache
*.ilk
*.log
*.lib
*.sbr
*.sdf
*.opensdf
*.unsuccessfulbuild
ipch/
*.idb
*.pgc
*.pgd
*.rsp
*.tmp
*.tmp_proj
*.log
*.vspscc
*.vssscc
.builds
*.pidb
*.svclog
*.scc

# Xcode
*.xcodeproj/
*.xcworkspace/
DerivedData/
*.hmap
*.ipa
*.dSYM.zip
*.dSYM

# Code::Blocks
*.depend
*.layout
*.cbp

# Qt Creator
*.pro.user
*.pro.user.*
*.qbs.user
*.qbs.user.*
*.moc
*.moc.cpp
*.qm
*.prl

# Node.js (for documentation tooling)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.eslintcache
.node_repl_history
*.tgz
.yarn-integrity
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Documentation build
docs/.vitepress/dist/
docs/.vitepress/cache/
docs/api/generated/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini

# Package managers
vcpkg_installed/
conan.lock
conanbuildinfo.*
conaninfo.txt
graph_info.json

# Testing
Testing/
CTestTestfile.cmake
*.gcov
*.gcda
*.gcno
coverage.info
coverage/

# Temporary files
*.tmp
*.temp
*.bak
*.backup
*~

# Logs
*.log
logs/

# Archives
*.zip
*.tar.gz
*.rar
*.7z

# Generated documentation
doxygen_output/
html/
latex/

# Profiling
*.prof
*.gprof
callgrind.out.*
perf.data*

# Sanitizer output
*.san

# CPack
_CPack_Packages/
*.dmg
*.rpm
*.deb
*.tar.Z

# Local configuration
local.properties
.env.local

# Benchmark results
benchmark_results/
*.benchmark

# Cache directories
.cache/
cache/

# Lock files (keep package-lock.json for reproducible builds)
yarn.lock
pnpm-lock.yaml
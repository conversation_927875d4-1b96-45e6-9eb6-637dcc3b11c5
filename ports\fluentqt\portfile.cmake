# vcpkg portfile for FluentQt

vcpkg_from_github(
    OUT_SOURCE_PATH SOURCE_PATH
    REPO ElementAstro/element-fluent-ui
    REF "v${VERSION}"
    SHA512 0  # This will be updated when creating the actual port
    HEAD_REF main
)

# Check for required Qt6 components
vcpkg_find_acquire_program(PKGCONFIG)

# Configure CMake options
set(FEATURE_OPTIONS "")

# Optional features
if("charts" IN_LIST FEATURES)
    list(APPEND FEATURE_OPTIONS "-DFLUENTQT_ENABLE_CHARTS=ON")
else()
    list(APPEND FEATURE_OPTIONS "-DFLUENTQT_ENABLE_CHARTS=OFF")
endif()

if("multimedia" IN_LIST FEATURES)
    list(APPEND FEATURE_OPTIONS "-DFLUENTQT_ENABLE_MULTIMEDIA=ON")
else()
    list(APPEND FEATURE_OPTIONS "-DFLUENTQT_ENABLE_MULTIMEDIA=OFF")
endif()

if("network" IN_LIST FEATURES)
    list(APPEND FEATURE_OPTIONS "-DFLUENTQT_ENABLE_NETWORK=ON")
else()
    list(APPEND FEATURE_OPTIONS "-DFLUENTQT_ENABLE_NETWORK=OFF")
endif()

# Configure the project
vcpkg_cmake_configure(
    SOURCE_PATH "${SOURCE_PATH}"
    OPTIONS
        ${FEATURE_OPTIONS}
        -DFLUENTQT_BUILD_EXAMPLES=OFF
        -DFLUENTQT_BUILD_TESTS=OFF
        -DFLUENTQT_BUILD_DOCS=OFF
        -DFLUENTQT_INSTALL=ON
        -DFLUENTQT_BUILD_SHARED=ON
    MAYBE_UNUSED_VARIABLES
        FLUENTQT_ENABLE_CHARTS
        FLUENTQT_ENABLE_MULTIMEDIA
        FLUENTQT_ENABLE_NETWORK
)

# Build the project
vcpkg_cmake_build()

# Install the project
vcpkg_cmake_install()

# Fix CMake targets and config files
vcpkg_cmake_config_fixup(
    PACKAGE_NAME FluentQt
    CONFIG_PATH lib/cmake/FluentQt
)

# Fix pkg-config files
vcpkg_fixup_pkgconfig()

# Remove debug includes and tools
file(REMOVE_RECURSE "${CURRENT_PACKAGES_DIR}/debug/include")
file(REMOVE_RECURSE "${CURRENT_PACKAGES_DIR}/debug/share")

# Handle copyright
vcpkg_install_copyright(FILE_LIST "${SOURCE_PATH}/LICENSE")

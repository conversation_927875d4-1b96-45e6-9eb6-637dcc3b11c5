{
  "recommendations": [
    // C++ Development
    "ms-vscode.cpptools",
    "ms-vscode.cpptools-extension-pack",
    "llvm-vs-code-extensions.vscode-clangd",
    
    // CMake
    "ms-vscode.cmake-tools",
    "twxs.cmake",
    
    // Qt Development
    "tonka3000.qtvsctools",
    "ms-vscode.qt-tools",
    
    // Git
    "eamodio.gitlens",
    "mhutchie.git-graph",
    
    // Documentation
    "yzhang.markdown-all-in-one",
    "davidanson.vscode-markdownlint",
    "bierner.markdown-mermaid",
    "shd101wyy.markdown-preview-enhanced",
    
    // Formatting & Linting
    "esbenp.prettier-vscode",
    "editorconfig.editorconfig",
    "ms-vscode.vscode-json",
    
    // Productivity
    "ms-vscode.hexeditor",
    "gruntfuggly.todo-tree",
    "streetsidesoftware.code-spell-checker",
    "aaron-bond.better-comments",
    
    // Themes & UI
    "pkief.material-icon-theme",
    "github.github-vscode-theme",
    
    // Testing
    "ms-vscode.test-adapter-converter",
    "hbenl.vscode-test-explorer",
    
    // Debugging
    "vadimcn.vscode-lldb",
    
    // Documentation Generation
    "cschlosser.doxdocgen",
    
    // YAML
    "redhat.vscode-yaml",
    
    // Docker (for CI/CD)
    "ms-azuretools.vscode-docker"
  ],
  "unwantedRecommendations": [
    "ms-vscode.vscode-typescript-next"
  ]
}

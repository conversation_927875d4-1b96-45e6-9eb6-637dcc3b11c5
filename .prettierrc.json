{"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2, "useTabs": false, "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "endOfLine": "lf", "embeddedLanguageFormatting": "auto", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "jsxSingleQuote": true, "proseWrap": "preserve", "quoteProps": "as-needed", "requirePragma": false, "vueIndentScriptAndStyle": false, "overrides": [{"files": "*.md", "options": {"printWidth": 100, "proseWrap": "always", "tabWidth": 2}}, {"files": "*.{yml,yaml}", "options": {"tabWidth": 2, "singleQuote": false}}, {"files": "*.json", "options": {"tabWidth": 2, "trailingComma": "none"}}]}
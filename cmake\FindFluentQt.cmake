# FindFluentQt.cmake - Find FluentQt library
#
# This module defines:
#  FluentQt_FOUND - True if FluentQt is found
#  FluentQt_INCLUDE_DIRS - Include directories for FluentQt
#  FluentQt_LIBRARIES - Libraries to link against
#  FluentQt_VERSION - Version of FluentQt found
#  FluentQt_VERSION_MAJOR - Major version number
#  FluentQt_VERSION_MINOR - Minor version number
#  FluentQt_VERSION_PATCH - Patch version number
#
# This module also defines the following imported targets:
#  FluentQt::FluentQt - The main FluentQt library
#
# You can specify the following variables to help guide the search:
#  FluentQt_ROOT - Root directory to search for FluentQt
#  FluentQt_DIR - Directory containing FluentQtConfig.cmake

# Use modern CMake config if available
find_package(FluentQt CONFIG QUIET)

if(FluentQt_FOUND)
    # Modern CMake config found, we're done
    return()
endif()

# Fallback to manual search
include(FindPackageHandleStandardArgs)

# Find the header files
find_path(FluentQt_INCLUDE_DIR
    NAMES FluentQt/FluentQt.h
    HINTS
        ${FluentQt_ROOT}
        $ENV{FluentQt_ROOT}
        ${FluentQt_DIR}
        $ENV{FluentQt_DIR}
    PATH_SUFFIXES
        include
    DOC "FluentQt include directory"
)

# Find the library
find_library(FluentQt_LIBRARY
    NAMES FluentQt libFluentQt
    HINTS
        ${FluentQt_ROOT}
        $ENV{FluentQt_ROOT}
        ${FluentQt_DIR}
        $ENV{FluentQt_DIR}
    PATH_SUFFIXES
        lib
        lib64
        bin
    DOC "FluentQt library"
)

# Extract version information
if(FluentQt_INCLUDE_DIR AND EXISTS "${FluentQt_INCLUDE_DIR}/FluentQt/FluentQt.h")
    file(READ "${FluentQt_INCLUDE_DIR}/FluentQt/FluentQt.h" _fluentqt_header_contents)
    
    string(REGEX MATCH "#define FLUENTQT_VERSION_MAJOR ([0-9]+)" _match "${_fluentqt_header_contents}")
    if(_match)
        set(FluentQt_VERSION_MAJOR "${CMAKE_MATCH_1}")
    endif()
    
    string(REGEX MATCH "#define FLUENTQT_VERSION_MINOR ([0-9]+)" _match "${_fluentqt_header_contents}")
    if(_match)
        set(FluentQt_VERSION_MINOR "${CMAKE_MATCH_1}")
    endif()
    
    string(REGEX MATCH "#define FLUENTQT_VERSION_PATCH ([0-9]+)" _match "${_fluentqt_header_contents}")
    if(_match)
        set(FluentQt_VERSION_PATCH "${CMAKE_MATCH_1}")
    endif()
    
    if(FluentQt_VERSION_MAJOR AND FluentQt_VERSION_MINOR AND FluentQt_VERSION_PATCH)
        set(FluentQt_VERSION "${FluentQt_VERSION_MAJOR}.${FluentQt_VERSION_MINOR}.${FluentQt_VERSION_PATCH}")
    endif()
    
    unset(_fluentqt_header_contents)
    unset(_match)
endif()

# Handle standard arguments
find_package_handle_standard_args(FluentQt
    REQUIRED_VARS
        FluentQt_LIBRARY
        FluentQt_INCLUDE_DIR
    VERSION_VAR
        FluentQt_VERSION
)

if(FluentQt_FOUND)
    # Set the output variables
    set(FluentQt_INCLUDE_DIRS ${FluentQt_INCLUDE_DIR})
    set(FluentQt_LIBRARIES ${FluentQt_LIBRARY})
    
    # Create imported target
    if(NOT TARGET FluentQt::FluentQt)
        add_library(FluentQt::FluentQt UNKNOWN IMPORTED)
        set_target_properties(FluentQt::FluentQt PROPERTIES
            IMPORTED_LOCATION "${FluentQt_LIBRARY}"
            INTERFACE_INCLUDE_DIRECTORIES "${FluentQt_INCLUDE_DIR}"
        )
        
        # Find and link Qt6 dependencies
        find_package(Qt6 REQUIRED COMPONENTS Core Widgets Gui PrintSupport)
        set_property(TARGET FluentQt::FluentQt PROPERTY
            INTERFACE_LINK_LIBRARIES
                Qt6::Core
                Qt6::Widgets
                Qt6::Gui
                Qt6::PrintSupport
        )
        
        # Platform-specific libraries
        if(WIN32)
            set_property(TARGET FluentQt::FluentQt APPEND PROPERTY
                INTERFACE_LINK_LIBRARIES dwmapi user32 gdi32
            )
        elseif(APPLE)
            find_library(COCOA_LIBRARY Cocoa)
            if(COCOA_LIBRARY)
                set_property(TARGET FluentQt::FluentQt APPEND PROPERTY
                    INTERFACE_LINK_LIBRARIES ${COCOA_LIBRARY}
                )
            endif()
        endif()
        
        # Check for optional Qt6 components
        find_package(Qt6 QUIET COMPONENTS Charts)
        if(Qt6Charts_FOUND)
            set_property(TARGET FluentQt::FluentQt APPEND PROPERTY
                INTERFACE_LINK_LIBRARIES Qt6::Charts
            )
            set_property(TARGET FluentQt::FluentQt APPEND PROPERTY
                INTERFACE_COMPILE_DEFINITIONS FLUENTQT_CHARTS_AVAILABLE
            )
        endif()
        
        find_package(Qt6 QUIET COMPONENTS Multimedia)
        if(Qt6Multimedia_FOUND)
            set_property(TARGET FluentQt::FluentQt APPEND PROPERTY
                INTERFACE_LINK_LIBRARIES Qt6::Multimedia
            )
            set_property(TARGET FluentQt::FluentQt APPEND PROPERTY
                INTERFACE_COMPILE_DEFINITIONS FLUENTQT_MULTIMEDIA_AVAILABLE
            )
        endif()
        
        find_package(Qt6 QUIET COMPONENTS Network)
        if(Qt6Network_FOUND)
            set_property(TARGET FluentQt::FluentQt APPEND PROPERTY
                INTERFACE_LINK_LIBRARIES Qt6::Network
            )
            set_property(TARGET FluentQt::FluentQt APPEND PROPERTY
                INTERFACE_COMPILE_DEFINITIONS FLUENTQT_NETWORK_AVAILABLE
            )
        endif()
    endif()
endif()

# Mark variables as advanced
mark_as_advanced(
    FluentQt_INCLUDE_DIR
    FluentQt_LIBRARY
)

name: 🐛 Bug Report
description: Report a bug or unexpected behavior
title: "[Bug]: "
labels: ["bug", "needs-triage"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        Thank you for taking the time to report a bug! Please fill out the information below to help us understand and reproduce the issue.

  - type: checkboxes
    id: prerequisites
    attributes:
      label: Prerequisites
      description: Please confirm the following before submitting a bug report
      options:
        - label: I have searched existing issues to ensure this bug hasn't been reported before
          required: true
        - label: I have read the documentation and troubleshooting guide
          required: true
        - label: I am using a supported version of Qt6 (6.2.0 or later)
          required: true

  - type: textarea
    id: description
    attributes:
      label: Bug Description
      description: A clear and concise description of what the bug is
      placeholder: Describe the bug...
    validations:
      required: true

  - type: textarea
    id: reproduction
    attributes:
      label: Steps to Reproduce
      description: Detailed steps to reproduce the behavior
      placeholder: |
        1. Create a FluentButton with...
        2. Set the property...
        3. Call the method...
        4. See error
    validations:
      required: true

  - type: textarea
    id: expected
    attributes:
      label: Expected Behavior
      description: What you expected to happen
      placeholder: Describe what should happen...
    validations:
      required: true

  - type: textarea
    id: actual
    attributes:
      label: Actual Behavior
      description: What actually happened
      placeholder: Describe what actually happens...
    validations:
      required: true

  - type: textarea
    id: code
    attributes:
      label: Minimal Code Example
      description: Please provide a minimal code example that reproduces the issue
      render: cpp
      placeholder: |
        #include "FluentQt/FluentQt.h"
        
        int main() {
            // Your minimal reproduction code here
            return 0;
        }

  - type: dropdown
    id: component
    attributes:
      label: Affected Component
      description: Which FluentQt component is affected?
      options:
        - Core System
        - Animation System
        - Styling/Theming
        - Form Components (Button, TextInput, etc.)
        - Layout Components (Card, Panel, etc.)
        - Feedback Components (ProgressBar, Toast, etc.)
        - Navigation Components
        - Accessibility Features
        - Build System
        - Documentation
        - Other (please specify in description)
    validations:
      required: true

  - type: dropdown
    id: severity
    attributes:
      label: Severity
      description: How severe is this bug?
      options:
        - Critical (Application crashes, data loss)
        - High (Major functionality broken)
        - Medium (Minor functionality affected)
        - Low (Cosmetic issue, workaround available)
    validations:
      required: true

  - type: input
    id: fluentqt-version
    attributes:
      label: FluentQt Version
      description: Which version of FluentQt are you using?
      placeholder: "1.0.0"
    validations:
      required: true

  - type: input
    id: qt-version
    attributes:
      label: Qt Version
      description: Which version of Qt6 are you using?
      placeholder: "6.5.0"
    validations:
      required: true

  - type: dropdown
    id: os
    attributes:
      label: Operating System
      description: Which operating system are you using?
      options:
        - Windows 10
        - Windows 11
        - macOS 12 (Monterey)
        - macOS 13 (Ventura)
        - macOS 14 (Sonoma)
        - Ubuntu 20.04
        - Ubuntu 22.04
        - Ubuntu 24.04
        - Other Linux Distribution
        - Other (please specify in additional context)
    validations:
      required: true

  - type: dropdown
    id: compiler
    attributes:
      label: Compiler
      description: Which compiler are you using?
      options:
        - MSVC 2019
        - MSVC 2022
        - GCC 10
        - GCC 11
        - GCC 12
        - GCC 13
        - Clang 13
        - Clang 14
        - Clang 15
        - Clang 16
        - Other (please specify in additional context)
    validations:
      required: true

  - type: dropdown
    id: build-type
    attributes:
      label: Build Configuration
      description: Which build configuration are you using?
      options:
        - Debug
        - Release
        - RelWithDebInfo
        - MinSizeRel
    validations:
      required: true

  - type: textarea
    id: error-output
    attributes:
      label: Error Output
      description: If applicable, paste any error messages, stack traces, or console output
      render: text
      placeholder: Paste error output here...

  - type: textarea
    id: additional-context
    attributes:
      label: Additional Context
      description: Add any other context about the problem here (screenshots, logs, etc.)
      placeholder: Any additional information that might help...

  - type: checkboxes
    id: contribution
    attributes:
      label: Contribution
      description: Would you be willing to contribute a fix for this issue?
      options:
        - label: I would be willing to submit a pull request to fix this issue
          required: false

// src/Core/FluentPerformance.cpp
#include "FluentQt/Core/FluentPerformance.h"
#include <QApplication>
#include <QProcess>
#include <QDebug>
#include <QMutexLocker>
#include <QSysInfo>
#include <algorithm>

#ifdef Q_OS_WIN
#include <windows.h>
#include <psapi.h>
#elif defined(Q_OS_LINUX)
#include <unistd.h>
#include <fstream>
#elif defined(Q_OS_MACOS)
#include <mach/mach.h>
#include <sys/sysctl.h>
#endif

namespace FluentQt::Core {

FluentPerformanceMonitor& FluentPerformanceMonitor::instance() {
    static FluentPerformanceMonitor instance;
    return instance;
}

FluentPerformanceMonitor::FluentPerformanceMonitor() {
    initializeMonitoring();
}

void FluentPerformanceMonitor::initializeMonitoring() {
    // Initialize timers
    m_frameTimer = new QTimer(this);
    m_performanceTimer = new QTimer(this);
    m_metricsTimer = new QTimer(this);
    m_cpuTimer = new QTimer(this);
    m_gpuTimer = new QTimer(this);
    m_memoryPressureTimer = new QTimer(this);
    m_thermalTimer = new QTimer(this);
    m_networkTimer = new QTimer(this);
    m_batteryTimer = new QTimer(this);
    m_analysisTimer = new QTimer(this);

    // Connect signals
    connect(m_frameTimer, &QTimer::timeout, this, &FluentPerformanceMonitor::updateFrameRate);
    connect(m_performanceTimer, &QTimer::timeout, this, &FluentPerformanceMonitor::checkPerformance);
    connect(m_metricsTimer, &QTimer::timeout, this, &FluentPerformanceMonitor::updateMetrics);
    connect(m_cpuTimer, &QTimer::timeout, this, &FluentPerformanceMonitor::updateCpuUsage);
    connect(m_gpuTimer, &QTimer::timeout, this, &FluentPerformanceMonitor::updateGpuUsage);
    connect(m_memoryPressureTimer, &QTimer::timeout, this, &FluentPerformanceMonitor::updateMemoryPressure);
    connect(m_thermalTimer, &QTimer::timeout, this, &FluentPerformanceMonitor::updateThermalState);
    connect(m_networkTimer, &QTimer::timeout, this, &FluentPerformanceMonitor::updateNetworkMetrics);
    connect(m_batteryTimer, &QTimer::timeout, this, &FluentPerformanceMonitor::updateBatteryStatus);
    connect(m_analysisTimer, &QTimer::timeout, this, &FluentPerformanceMonitor::analyzePerformanceTrends);

    // Initialize metrics
    m_metrics = PerformanceMetrics{};
    m_previousMetrics = PerformanceMetrics{};
    trackMemoryUsage();

    // Initialize platform-specific monitoring
    initializePlatformMonitoring();
}

void FluentPerformanceMonitor::startFrameRateMonitoring() {
    m_frameTimeTracker.start();
    m_frameTimer->start(16); // ~60 FPS
    m_performanceTimer->start(1000); // Check every second
    m_metricsTimer->start(500); // Update metrics twice per second
    m_frameCount = 0;
}

void FluentPerformanceMonitor::stopFrameRateMonitoring() {
    m_frameTimer->stop();
    m_performanceTimer->stop();
    m_metricsTimer->stop();
}

void FluentPerformanceMonitor::startCpuMonitoring() {
    m_cpuTimer->start(2000); // Update CPU usage every 2 seconds
}

void FluentPerformanceMonitor::stopCpuMonitoring() {
    m_cpuTimer->stop();
}

// Component registration and monitoring
void FluentPerformanceMonitor::registerComponent(const QString& componentName, QObject* component) {
    QMutexLocker locker(&m_componentMutex);
    m_registeredComponents[componentName] = component;
    m_componentData[componentName] = ComponentPerformanceData{componentName};
}

void FluentPerformanceMonitor::unregisterComponent(const QString& componentName) {
    QMutexLocker locker(&m_componentMutex);
    m_registeredComponents.erase(componentName);
    m_componentData.erase(componentName);
}

void FluentPerformanceMonitor::recordComponentRender(const QString& componentName, std::chrono::milliseconds renderTime) {
    QMutexLocker locker(&m_componentMutex);
    auto& data = m_componentData[componentName];
    data.renderCount++;
    data.totalRenderTime += renderTime;
    data.averageRenderTime = data.totalRenderTime / data.renderCount;
    data.maxRenderTime = std::max(data.maxRenderTime, renderTime);
    data.minRenderTime = std::min(data.minRenderTime, renderTime);
    data.lastRenderTime = std::chrono::steady_clock::now();

    // Update performance scores
    data.renderPerformanceScore = calculateComponentPerformanceScore(componentName);
    data.overallPerformanceScore = (data.renderPerformanceScore + data.memoryEfficiencyScore) / 2.0;

    // Check for performance issues
    if (renderTime > std::chrono::milliseconds(16)) { // > 16ms = < 60fps
        data.hasPerformanceIssues = true;
        emit componentPerformanceIssue(componentName,
            QString("Slow render: %1ms").arg(renderTime.count()));
    }

    // Generate optimization suggestions
    auto suggestions = generateOptimizationSuggestions(componentName);
    if (!suggestions.isEmpty()) {
        data.optimizationSuggestions = suggestions;
        emit componentOptimizationSuggestion(componentName, suggestions);
    }
}

ComponentPerformanceData FluentPerformanceMonitor::getComponentData(const QString& componentName) const {
    QMutexLocker locker(&m_componentMutex);
    auto it = m_componentData.find(componentName);
    if (it != m_componentData.end()) {
        return it->second;
    } else {
        ComponentPerformanceData defaultData;
        defaultData.componentName = componentName;
        defaultData.componentType = "Unknown";
        defaultData.creationTime = std::chrono::steady_clock::now();
        return defaultData;
    }
}

QList<ComponentPerformanceData> FluentPerformanceMonitor::getAllComponentData() const {
    QMutexLocker locker(&m_componentMutex);
    QList<ComponentPerformanceData> result;
    for (const auto& [name, data] : m_componentData) {
        result.append(data);
    }
    return result;
}

// Memory tracking
size_t FluentPerformanceMonitor::currentMemoryUsage() const {
#ifdef Q_OS_WIN
    PROCESS_MEMORY_COUNTERS pmc;
    if (GetProcessMemoryInfo(GetCurrentProcess(), &pmc, sizeof(pmc))) {
        return pmc.WorkingSetSize;
    }
#elif defined(Q_OS_LINUX)
    std::ifstream file("/proc/self/status");
    std::string line;
    while (std::getline(file, line)) {
        if (line.substr(0, 6) == "VmRSS:") {
            return std::stoul(line.substr(7)) * 1024; // Convert KB to bytes
        }
    }
#elif defined(Q_OS_MACOS)
    task_basic_info info;
    mach_msg_type_number_t size = sizeof(info);
    if (task_info(mach_task_self(), TASK_BASIC_INFO, (task_info_t)&info, &size) == KERN_SUCCESS) {
        return info.resident_size;
    }
#endif
    return 0;
}

void FluentPerformanceMonitor::trackMemoryUsage() {
    m_metrics.memoryUsage = currentMemoryUsage();
    m_metrics.peakMemoryUsage = std::max(m_metrics.peakMemoryUsage, m_metrics.memoryUsage);
}

void FluentPerformanceMonitor::resetPeakMemoryUsage() {
    m_metrics.peakMemoryUsage = m_metrics.memoryUsage;
}

// Profiling
void FluentPerformanceMonitor::beginProfile(const QString& operation) {
    QMutexLocker locker(&m_profileMutex);
    m_profileTimers[operation].start();
}

void FluentPerformanceMonitor::endProfile(const QString& operation) {
    QMutexLocker locker(&m_profileMutex);
    auto it = m_profileTimers.find(operation);
    if (it != m_profileTimers.end()) {
        auto elapsed = std::chrono::milliseconds(it->second.elapsed());
        m_profileResults[operation] = elapsed;
    }
}

std::chrono::milliseconds FluentPerformanceMonitor::getProfileTime(const QString& operation) const {
    QMutexLocker locker(&m_profileMutex);
    auto it = m_profileResults.find(operation);
    return (it != m_profileResults.end()) ? it->second : std::chrono::milliseconds(0);
}

void FluentPerformanceMonitor::clearProfileData() {
    QMutexLocker locker(&m_profileMutex);
    m_profileTimers.clear();
    m_profileResults.clear();
}

// Benchmarking
void FluentPerformanceMonitor::runBenchmark(const QString& benchmarkName, std::function<void()> benchmark) {
    QElapsedTimer timer;
    size_t initialMemory = currentMemoryUsage();

    timer.start();
    benchmark();
    auto duration = std::chrono::milliseconds(timer.elapsed());

    size_t finalMemory = currentMemoryUsage();
    size_t memoryAllocated = (finalMemory > initialMemory) ? finalMemory - initialMemory : 0;

    BenchmarkResult result{
        benchmarkName,
        duration,
        1000.0 / duration.count(), // Operations per second
        memoryAllocated,
        duration < std::chrono::milliseconds(100), // Pass if < 100ms
        QString("Duration: %1ms, Memory: %2 bytes").arg(duration.count()).arg(memoryAllocated)
    };

    QMutexLocker locker(&m_benchmarkMutex);
    m_benchmarkResults.append(result);
    emit benchmarkCompleted(result);
}

void FluentPerformanceMonitor::runComponentBenchmark(const QString& componentName) {
    Q_UNUSED(componentName)
    // This would run component-specific benchmarks
    // Implementation depends on component type
    // TODO: Implement component-specific benchmarks
}

QList<BenchmarkResult> FluentPerformanceMonitor::getBenchmarkResults() const {
    QMutexLocker locker(&m_benchmarkMutex);
    return m_benchmarkResults;
}

void FluentPerformanceMonitor::clearBenchmarkResults() {
    QMutexLocker locker(&m_benchmarkMutex);
    m_benchmarkResults.clear();
}

// Optimization hints
bool FluentPerformanceMonitor::shouldSkipAnimation() const {
    return m_metrics.frameRate < LOW_FPS_THRESHOLD || m_metrics.isLowPerformanceMode;
}

bool FluentPerformanceMonitor::shouldReduceEffects() const {
    return m_metrics.frameRate < 45.0 || m_metrics.isLowPerformanceMode;
}

int FluentPerformanceMonitor::recommendedAnimationDuration() const {
    if (m_metrics.isLowPerformanceMode) {
        return 50;
    } else if (m_metrics.frameRate >= 60.0) {
        return 250; // Full duration
    } else if (m_metrics.frameRate >= 30.0) {
        return 150; // Reduced duration
    } else {
        return 50;  // Minimal duration
    }
}

bool FluentPerformanceMonitor::shouldUseLowQualityRendering() const {
    return m_metrics.frameRate < CRITICAL_FPS_THRESHOLD ||
           m_metrics.cpuUsage > HIGH_CPU_THRESHOLD ||
           m_metrics.isLowPerformanceMode;
}

int FluentPerformanceMonitor::recommendedMaxAnimations() const {
    if (m_metrics.isLowPerformanceMode) {
        return 1;
    } else if (m_metrics.frameRate >= 60.0) {
        return 10;
    } else if (m_metrics.frameRate >= 30.0) {
        return 5;
    } else {
        return 2;
    }
}

// Performance mode management
void FluentPerformanceMonitor::enableLowPerformanceMode(bool enable) {
    if (m_metrics.isLowPerformanceMode != enable) {
        m_metrics.isLowPerformanceMode = enable;
        if (enable) {
            optimizeForLowPerformance();
        }
        emit metricsUpdated(m_metrics);
    }
}

// Timer callbacks
void FluentPerformanceMonitor::updateFrameRate() {
    m_frameCount++;

    if (m_frameTimeTracker.elapsed() >= 1000) { // Update every second
        m_metrics.frameRate = (m_frameCount * 1000.0) / m_frameTimeTracker.elapsed();
        emit frameRateChanged(m_metrics.frameRate);

        m_frameCount = 0;
        m_frameTimeTracker.restart();
    }
}

void FluentPerformanceMonitor::checkPerformance() {
    detectPerformanceIssues();

    if (m_metrics.frameRate < CRITICAL_FPS_THRESHOLD) {
        emit performanceCritical("Critical frame rate detected. Enabling low performance mode.");
        enableLowPerformanceMode(true);
    } else if (m_metrics.frameRate < LOW_FPS_THRESHOLD) {
        emit performanceWarning("Low frame rate detected. Consider reducing visual effects.");
    }

    if (m_metrics.memoryUsage > HIGH_MEMORY_THRESHOLD) {
        emit performanceWarning(QString("High memory usage: %1 MB")
            .arg(m_metrics.memoryUsage / (1024 * 1024)));
    }

    if (m_metrics.cpuUsage > HIGH_CPU_THRESHOLD) {
        emit performanceWarning(QString("High CPU usage: %1%").arg(m_metrics.cpuUsage));
    }
}

void FluentPerformanceMonitor::updateMetrics() {
    trackMemoryUsage();
    m_metrics.renderCalls++; // This would be updated by components
    emit metricsUpdated(m_metrics);
}

void FluentPerformanceMonitor::updateCpuUsage() {
    // Platform-specific CPU usage calculation
#ifdef Q_OS_WIN
    // Windows implementation would go here
    m_metrics.cpuUsage = 0.0; // Placeholder
#elif defined(Q_OS_LINUX)
    // Linux implementation would go here
    m_metrics.cpuUsage = 0.0; // Placeholder
#elif defined(Q_OS_MACOS)
    // macOS implementation would go here
    m_metrics.cpuUsage = 0.0; // Placeholder
#endif
}

// Private helper methods
void FluentPerformanceMonitor::detectPerformanceIssues() {
    // Check for performance issues across all components
    QMutexLocker locker(&m_componentMutex);
    for (auto& [name, data] : m_componentData) {
        if (data.averageRenderTime > std::chrono::milliseconds(16)) {
            data.hasPerformanceIssues = true;
        }
    }
}

void FluentPerformanceMonitor::optimizeForLowPerformance() {
    // Automatically optimize settings for low performance
    // This could disable certain effects, reduce animation quality, etc.
    qDebug() << "FluentPerformanceMonitor: Optimizing for low performance";
}

// Enhanced monitoring methods
void FluentPerformanceMonitor::startGpuMonitoring() {
    m_gpuMonitoringEnabled = true;
    m_gpuTimer->start(3000); // Update GPU usage every 3 seconds
}

void FluentPerformanceMonitor::stopGpuMonitoring() {
    m_gpuMonitoringEnabled = false;
    m_gpuTimer->stop();
}

void FluentPerformanceMonitor::startMemoryPressureMonitoring() {
    m_memoryPressureMonitoringEnabled = true;
    m_memoryPressureTimer->start(2000); // Update every 2 seconds
}

void FluentPerformanceMonitor::stopMemoryPressureMonitoring() {
    m_memoryPressureMonitoringEnabled = false;
    m_memoryPressureTimer->stop();
}

void FluentPerformanceMonitor::startThermalMonitoring() {
    m_thermalMonitoringEnabled = true;
    m_thermalTimer->start(5000); // Update every 5 seconds
}

void FluentPerformanceMonitor::stopThermalMonitoring() {
    m_thermalMonitoringEnabled = false;
    m_thermalTimer->stop();
}

void FluentPerformanceMonitor::startNetworkMonitoring() {
    m_networkMonitoringEnabled = true;
    m_networkTimer->start(1000); // Update every second
}

void FluentPerformanceMonitor::stopNetworkMonitoring() {
    m_networkMonitoringEnabled = false;
    m_networkTimer->stop();
}

void FluentPerformanceMonitor::startBatteryMonitoring() {
    m_batteryMonitoringEnabled = true;
    m_batteryTimer->start(10000); // Update every 10 seconds
}

void FluentPerformanceMonitor::stopBatteryMonitoring() {
    m_batteryMonitoringEnabled = false;
    m_batteryTimer->stop();
}

void FluentPerformanceMonitor::recordInputEvent(const QString& eventType) {
    Q_UNUSED(eventType)
    auto now = std::chrono::steady_clock::now();
    m_inputEventTimes.append(now);

    // Keep only recent events (last 5 seconds)
    auto cutoff = now - std::chrono::seconds(5);
    m_inputEventTimes.erase(
        std::remove_if(m_inputEventTimes.begin(), m_inputEventTimes.end(),
                      [cutoff](const auto& time) { return time < cutoff; }),
        m_inputEventTimes.end());
}

void FluentPerformanceMonitor::recordInputLatency(std::chrono::milliseconds latency) {
    m_inputLatencies.append(latency);

    // Keep only recent latencies (last 100 events)
    if (m_inputLatencies.size() > 100) {
        m_inputLatencies.removeFirst();
    }

    // Calculate average
    if (!m_inputLatencies.isEmpty()) {
        auto total = std::accumulate(m_inputLatencies.begin(), m_inputLatencies.end(),
                                   std::chrono::milliseconds(0));
        m_metrics.inputLatency = total / m_inputLatencies.size();
    }

    // Check for high latency
    if (latency > HIGH_INPUT_LATENCY_THRESHOLD) {
        emit inputLatencyWarning(latency);
    }
}

void FluentPerformanceMonitor::recordComponentUpdate(const QString& componentName, std::chrono::milliseconds updateTime) {
    QMutexLocker locker(&m_componentMutex);
    auto& data = m_componentData[componentName];
    data.updateCount++;
    data.totalUpdateTime += updateTime;
}

void FluentPerformanceMonitor::recordComponentLayout(const QString& componentName, std::chrono::milliseconds layoutTime) {
    QMutexLocker locker(&m_componentMutex);
    auto& data = m_componentData[componentName];
    data.layoutCount++;
    data.totalLayoutTime += layoutTime;
}

void FluentPerformanceMonitor::recordComponentInteraction(const QString& componentName, std::chrono::milliseconds responseTime) {
    QMutexLocker locker(&m_componentMutex);
    auto& data = m_componentData[componentName];
    data.userInteractions++;
    data.totalInteractionTime += responseTime;
    data.averageInteractionLatency = data.totalInteractionTime.count() / static_cast<double>(data.userInteractions);
}

void FluentPerformanceMonitor::setComponentVisibility(const QString& componentName, bool visible) {
    QMutexLocker locker(&m_componentMutex);
    auto& data = m_componentData[componentName];
    data.isVisible = visible;

    if (visible) {
        m_metrics.visibleComponents++;
    } else {
        m_metrics.visibleComponents = std::max(0, m_metrics.visibleComponents - 1);
    }
}

void FluentPerformanceMonitor::markComponentAsLazyLoaded(const QString& componentName, bool lazyLoaded) {
    QMutexLocker locker(&m_componentMutex);
    auto& data = m_componentData[componentName];
    data.isLazyLoaded = lazyLoaded;
}

double FluentPerformanceMonitor::calculateComponentPerformanceScore(const QString& componentName) const {
    QMutexLocker locker(&m_componentMutex);
    auto it = m_componentData.find(componentName);
    if (it == m_componentData.end()) {
        return 100.0;
    }

    const auto& data = it->second;
    double score = 100.0;

    // Penalize slow render times
    if (data.averageRenderTime > std::chrono::milliseconds(16)) {
        score -= (data.averageRenderTime.count() - 16) * 2.0;
    }

    // Penalize high memory usage
    if (data.memoryFootprint > 10 * 1024 * 1024) { // 10MB
        double memoryMB = static_cast<double>(data.memoryFootprint) / (1024 * 1024);
        score -= (memoryMB - 10.0) * 5.0;
    }

    // Penalize performance issues
    if (data.hasPerformanceIssues) {
        score -= 20.0;
    }

    return std::max(0.0, std::min(100.0, score));
}

double FluentPerformanceMonitor::calculateOverallPerformanceScore() const {
    double totalScore = 0.0;
    int componentCount = 0;

    QMutexLocker locker(&m_componentMutex);
    for (const auto& [name, data] : m_componentData) {
        totalScore += data.overallPerformanceScore;
        componentCount++;
    }

    if (componentCount == 0) {
        return 100.0;
    }

    double averageScore = totalScore / componentCount;

    // Adjust based on system metrics
    if (m_metrics.frameRate < LOW_FPS_THRESHOLD) {
        averageScore -= 20.0;
    }
    if (m_metrics.memoryPressure > HIGH_MEMORY_PRESSURE_THRESHOLD) {
        averageScore -= 15.0;
    }
    if (m_metrics.cpuUsage > HIGH_CPU_THRESHOLD) {
        averageScore -= 10.0;
    }

    return std::max(0.0, std::min(100.0, averageScore));
}

QStringList FluentPerformanceMonitor::generateOptimizationSuggestions(const QString& componentName) const {
    QStringList suggestions;

    QMutexLocker locker(&m_componentMutex);
    auto it = m_componentData.find(componentName);
    if (it == m_componentData.end()) {
        return suggestions;
    }

    const auto& data = it->second;

    if (data.averageRenderTime > std::chrono::milliseconds(16)) {
        suggestions << "Consider optimizing render operations or using caching";
    }

    if (data.memoryFootprint > 50 * 1024 * 1024) { // 50MB
        suggestions << "High memory usage detected - consider lazy loading or data optimization";
    }

    if (!data.isLazyLoaded && data.memoryFootprint > 10 * 1024 * 1024) {
        suggestions << "Consider implementing lazy loading for this component";
    }

    if (data.animationCount > 5) {
        suggestions << "Too many concurrent animations - consider reducing or staggering them";
    }

    if (data.userInteractions > 0 && data.averageInteractionLatency > 100.0) {
        suggestions << "High interaction latency - optimize event handling";
    }

    return suggestions;
}

QStringList FluentPerformanceMonitor::generateGlobalOptimizationSuggestions() const {
    QStringList suggestions;

    if (m_metrics.frameRate < LOW_FPS_THRESHOLD) {
        suggestions << "Enable low performance mode to improve frame rate";
    }

    if (m_metrics.memoryUsage > HIGH_MEMORY_THRESHOLD) {
        suggestions << "High memory usage - consider unloading unused components";
    }

    if (m_metrics.activeAnimations > recommendedMaxAnimations()) {
        suggestions << "Too many active animations - consider reducing concurrent animations";
    }

    if (m_metrics.inputLatency > HIGH_INPUT_LATENCY_THRESHOLD) {
        suggestions << "High input latency detected - optimize event processing";
    }

    if (m_metrics.isThrottling) {
        suggestions << "System throttling detected - reduce computational load";
    }

    return suggestions;
}

// Timer callback implementations
void FluentPerformanceMonitor::updateGpuUsage() {
    if (!m_gpuMonitoringEnabled) return;

    // Platform-specific GPU usage calculation
#ifdef Q_OS_WIN
    // Windows GPU monitoring implementation
    m_metrics.gpuUsage = 0.0; // Placeholder
#elif defined(Q_OS_LINUX)
    // Linux GPU monitoring implementation
    m_metrics.gpuUsage = 0.0; // Placeholder
#elif defined(Q_OS_MACOS)
    // macOS GPU monitoring implementation
    m_metrics.gpuUsage = 0.0; // Placeholder
#endif

    if (m_metrics.gpuUsage > HIGH_GPU_THRESHOLD) {
        emit performanceWarning(QString("High GPU usage: %1%").arg(m_metrics.gpuUsage));
    }
}

void FluentPerformanceMonitor::updateMemoryPressure() {
    if (!m_memoryPressureMonitoringEnabled) return;

    // Calculate memory pressure based on available vs used memory
    size_t totalMemory = 8ULL * 1024 * 1024 * 1024; // 8GB default, should be detected
    m_metrics.memoryPressure = static_cast<double>(m_metrics.memoryUsage) / totalMemory;

    if (m_metrics.memoryPressure > HIGH_MEMORY_PRESSURE_THRESHOLD) {
        emit memoryPressureChanged(m_metrics.memoryPressure);
        emit performanceWarning(QString("High memory pressure: %1%")
                               .arg(m_metrics.memoryPressure * 100, 0, 'f', 1));
    }
}

void FluentPerformanceMonitor::updateThermalState() {
    if (!m_thermalMonitoringEnabled) return;

    // Platform-specific thermal monitoring
#ifdef Q_OS_WIN
    // Windows thermal monitoring
    m_metrics.thermalState = 0.0; // Placeholder
#elif defined(Q_OS_LINUX)
    // Linux thermal monitoring
    m_metrics.thermalState = 0.0; // Placeholder
#elif defined(Q_OS_MACOS)
    // macOS thermal monitoring
    m_metrics.thermalState = 0.0; // Placeholder
#endif

    if (m_metrics.thermalState > HIGH_THERMAL_THRESHOLD) {
        emit thermalStateChanged(m_metrics.thermalState);
        if (!m_metrics.isThrottling) {
            m_metrics.isThrottling = true;
            emit throttlingDetected("High thermal state");
        }
    } else if (m_metrics.isThrottling && m_metrics.thermalState < HIGH_THERMAL_THRESHOLD - 10.0) {
        m_metrics.isThrottling = false;
    }
}

void FluentPerformanceMonitor::updateNetworkMetrics() {
    if (!m_networkMonitoringEnabled) return;

    // Network latency and throughput monitoring
    // This would typically ping a known server or measure local network performance
    m_metrics.networkLatency = std::chrono::milliseconds(50); // Placeholder

    if (m_metrics.networkLatency > HIGH_NETWORK_LATENCY_THRESHOLD) {
        emit networkLatencyChanged(m_metrics.networkLatency);
    }
}

void FluentPerformanceMonitor::updateBatteryStatus() {
    if (!m_batteryMonitoringEnabled) return;

    // Platform-specific battery monitoring
#ifdef Q_OS_WIN
    // Windows battery monitoring
    m_metrics.batteryLevel = 100.0; // Placeholder
    m_metrics.isLowPowerMode = false; // Placeholder
#elif defined(Q_OS_LINUX)
    // Linux battery monitoring
    m_metrics.batteryLevel = 100.0; // Placeholder
    m_metrics.isLowPowerMode = false; // Placeholder
#elif defined(Q_OS_MACOS)
    // macOS battery monitoring
    m_metrics.batteryLevel = 100.0; // Placeholder
    m_metrics.isLowPowerMode = false; // Placeholder
#endif

    if (m_metrics.batteryLevel < LOW_BATTERY_THRESHOLD) {
        emit batteryLevelChanged(m_metrics.batteryLevel);
        if (!m_metrics.isLowPowerMode) {
            m_metrics.isLowPowerMode = true;
            emit lowPowerModeChanged(true);
        }
    }
}

void FluentPerformanceMonitor::analyzePerformanceTrends() {
    // Store current metrics in history
    m_metricsHistory.append(m_metrics);

    // Keep only recent history
    if (m_metricsHistory.size() > MAX_HISTORY_SIZE) {
        m_metricsHistory.removeFirst();
    }

    // Detect performance degradation
    if (m_metricsHistory.size() >= 10) {
        auto recent = m_metricsHistory.last();
        auto baseline = m_metricsHistory[m_metricsHistory.size() - 10];

        if (detectPerformanceAnomaly(recent, baseline)) {
            // Calculate degradation percentage
            double fpsChange = (baseline.frameRate - recent.frameRate) / baseline.frameRate * 100.0;
            if (fpsChange > 10.0) {
                emit performanceDegradation("Frame Rate", fpsChange);
            }
        }
    }
}

void FluentPerformanceMonitor::detectPerformanceAnomalies() {
    // Detect memory leaks
    if (m_metricsHistory.size() >= 5) {
        auto current = m_metrics.memoryUsage;
        auto previous = m_metricsHistory[m_metricsHistory.size() - 5].memoryUsage;

        if (current > previous + MEMORY_LEAK_THRESHOLD) {
            m_metrics.memoryLeaks++;
            emit memoryLeakDetected("System", current - previous);
        }
    }
}

void FluentPerformanceMonitor::cleanupStaleData() {
    // Clean up old input event data
    auto now = std::chrono::steady_clock::now();
    auto cutoff = now - std::chrono::minutes(5);

    m_inputEventTimes.erase(
        std::remove_if(m_inputEventTimes.begin(), m_inputEventTimes.end(),
                      [cutoff](const auto& time) { return time < cutoff; }),
        m_inputEventTimes.end());

    // Clean up component data for destroyed components
    QMutexLocker locker(&m_componentMutex);
    for (auto it = m_componentData.begin(); it != m_componentData.end();) {
        auto componentIt = m_registeredComponents.find(it->first);
        if (componentIt == m_registeredComponents.end() || !componentIt->second) {
            it = m_componentData.erase(it);
        } else {
            ++it;
        }
    }
}

// Platform-specific monitoring implementations
void FluentPerformanceMonitor::initializePlatformMonitoring() {
    // Platform-specific initialization
#ifdef Q_OS_WIN
    // Windows-specific monitoring setup
#elif defined(Q_OS_LINUX)
    // Linux-specific monitoring setup
#elif defined(Q_OS_MACOS)
    // macOS-specific monitoring setup
#endif
}

void FluentPerformanceMonitor::cleanupPlatformMonitoring() {
    // Cleanup platform-specific resources
    if (m_platformMonitoringHandle) {
        // Platform-specific cleanup
        m_platformMonitoringHandle = nullptr;
    }
}

double FluentPerformanceMonitor::calculatePerformanceTrend(const QString& metric) const {
    if (m_metricsHistory.size() < 2) {
        return 0.0;
    }

    // Simple linear trend calculation
    auto recent = m_metricsHistory.last();
    auto baseline = m_metricsHistory.first();

    if (metric == "frameRate") {
        return (recent.frameRate - baseline.frameRate) / baseline.frameRate * 100.0;
    } else if (metric == "memoryUsage") {
        return (static_cast<double>(recent.memoryUsage) - static_cast<double>(baseline.memoryUsage)) /
               static_cast<double>(baseline.memoryUsage) * 100.0;
    } else if (metric == "cpuUsage") {
        return (recent.cpuUsage - baseline.cpuUsage) / baseline.cpuUsage * 100.0;
    }

    return 0.0;
}

bool FluentPerformanceMonitor::detectPerformanceAnomaly(const PerformanceMetrics& current,
                                                       const PerformanceMetrics& baseline) const {
    // Detect significant performance changes
    double fpsChange = std::abs(current.frameRate - baseline.frameRate) / baseline.frameRate;
    double memoryChange = std::abs(static_cast<double>(current.memoryUsage) - static_cast<double>(baseline.memoryUsage)) /
                         static_cast<double>(baseline.memoryUsage);
    double cpuChange = std::abs(current.cpuUsage - baseline.cpuUsage) / baseline.cpuUsage;

    // Consider it an anomaly if any metric changes by more than 20%
    return fpsChange > 0.2 || memoryChange > 0.2 || cpuChange > 0.2;
}

} // namespace FluentQt::Core
